#!/usr/bin/env python3
"""
Test Redis Connection

This script tests the Redis connection using the environment variables
to verify that the hybrid cache system can connect properly.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_redis_connection():
    """Test Redis connection with environment variables."""
    print("🔄 Testing Redis Connection")
    print("=" * 50)
    
    # Check environment variables
    redis_vars = {
        'REDIS_HOST': os.getenv('REDIS_HOST'),
        'REDIS_PORT': os.getenv('REDIS_PORT'),
        'REDIS_USERNAME': os.getenv('REDIS_USERNAME'),
        'REDIS_PASSWORD': os.getenv('REDIS_PASSWORD'),
    }
    
    print("Environment Variables:")
    for key, value in redis_vars.items():
        if value:
            display_value = value if key != 'REDIS_PASSWORD' else '*' * len(value)
            print(f"  ✅ {key}: {display_value}")
        else:
            print(f"  ❌ {key}: Not set")
    
    # Check if all required variables are set
    missing_vars = [k for k, v in redis_vars.items() if not v]
    if missing_vars:
        print(f"\n❌ Missing environment variables: {missing_vars}")
        print("Please set these variables in Railway:")
        print("  REDIS_HOST=redis-10798.c292.ap-southeast-1-1.ec2.redns.redis-cloud.com")
        print("  REDIS_PORT=10798")
        print("  REDIS_USERNAME=default")
        print("  REDIS_PASSWORD=DOZjOYjJLoBNir3mIrRSE94WzgGyCFQ5")
        return False
    
    # Test Redis import
    try:
        import redis.asyncio as redis
        print("\n✅ Redis package imported successfully")
    except ImportError as e:
        print(f"\n❌ Redis package not available: {e}")
        print("Redis should be installed as a main dependency now")
        return False
    
    # Test Redis connection
    try:
        print("\n🔄 Testing Redis connection...")
        
        redis_client = redis.Redis(
            host=redis_vars['REDIS_HOST'],
            port=int(redis_vars['REDIS_PORT']),
            username=redis_vars['REDIS_USERNAME'],
            password=redis_vars['REDIS_PASSWORD'],
            ssl=False,
            socket_timeout=5.0,
            socket_connect_timeout=5.0,
            decode_responses=True
        )
        
        # Test ping
        await redis_client.ping()
        print("✅ Redis ping successful!")
        
        # Test basic operations
        test_key = "test:connection:check"
        await redis_client.set(test_key, "working", ex=60)
        value = await redis_client.get(test_key)
        
        if value == "working":
            print("✅ Redis read/write operations working!")
        else:
            print(f"❌ Redis read/write test failed. Expected 'working', got: {value}")
            return False
        
        # Cleanup
        await redis_client.delete(test_key)
        await redis_client.close()
        
        print("✅ Redis connection test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        print("\nPossible issues:")
        print("  1. Environment variables not set correctly in Railway")
        print("  2. Redis Cloud service not accessible")
        print("  3. Network connectivity issues")
        print("  4. Incorrect credentials")
        return False


async def test_hybrid_cache_import():
    """Test hybrid cache system import."""
    print("\n🔄 Testing Hybrid Cache Import")
    print("=" * 50)
    
    try:
        from src.cache import (
            get_hybrid_cache,
            HybridCacheSystem,
            CacheResult,
            JobStatus
        )
        print("✅ Hybrid cache imports successful!")
        
        # Test initialization
        cache = await get_hybrid_cache()
        if cache.initialized:
            print("✅ Hybrid cache initialized successfully!")
            
            # Test health check
            health = await cache.health_check()
            print(f"✅ Cache health check: {health}")
            
            return True
        else:
            print("❌ Hybrid cache initialization failed")
            return False
            
    except Exception as e:
        print(f"❌ Hybrid cache import/init failed: {e}")
        return False


async def main():
    """Run all Redis tests."""
    print("🧪 Redis Connection & Hybrid Cache Tests")
    print("=" * 60)
    
    try:
        # Test Redis connection
        redis_ok = await test_redis_connection()
        
        # Test hybrid cache import
        cache_ok = await test_hybrid_cache_import()
        
        print("\n" + "=" * 60)
        if redis_ok and cache_ok:
            print("🎉 All tests passed!")
            print("✅ Redis connection working")
            print("✅ Hybrid cache system ready")
            print("\nThe 'Hybrid cache not available' error should be resolved after deployment.")
        else:
            print("❌ Some tests failed")
            if not redis_ok:
                print("  - Redis connection issues")
            if not cache_ok:
                print("  - Hybrid cache system issues")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
